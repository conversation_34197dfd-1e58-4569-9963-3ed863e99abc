/**
 * 玩具车蓝牙控制工具 - 新架构版本
 * 功能：每个指令独立广播，按钮状态数组管理
 * 协议：V1.10 玩具车蓝牙协议
 */

// 导入工具函数
import { generateData, getServiceUUIDs } from "../myutil/BLEUtil"  // BLE数据处理工具
import { ab2hex, inArray } from "../myutil/util"  // 处理转码工具函数
// 导入配置文件
import {
  DEFAULT_CONFIG,
  GEAR_SPEED_MAP,
  getCurrentConfig,
  saveConfig
} from './toyCarBleConfig'


// 玩具车蓝牙控制类 - 新架构
class ToyCarBleUnifiedNew {
  constructor() {
    // 🎯 使用配置文件中的默认配置
    this.config = getCurrentConfig()
    this.deviceAddress = 0x00  // 默认设备地址码 (用于指令生成)

    // 蓝牙状态
    this.isConnected = false  // 是否已连接
    this.isScanning = false  // 是否正在扫描设备
    this.discoveryStarted = false  // 是否已开始扫描
    this.discoveryReady = false   // 是否已准备好扫描设备
    this.advertiseReady = false     // 是否已准备好广播设备
    this.isRebuilding = false       // 是否正在重建服务器

    // 设备信息 (统一使用配置文件)
    this.targetDeviceName = this.config.deviceName
    this.targetAddress = this.config.address
    this.connectedDevice = null

    // 数据管理
    this.devices = []
    this.receiveDataList = []
    this.filterName = this.config.deviceName  // 🎯 统一使用配置文件中的设备名称

    // 🎯 设备搜索状态
    this.deviceFound = false           // 是否找到目标设备
    this.lastDeviceFoundTime = 0       // 最后发现设备的时间
    this.deviceSearchTimeout = 30000    // 设备搜索超时时间（30秒）



    // 🎯 新架构：独立广播管理
    this.broadcastServers = new Map()  // 存储每个指令的独立服务器
    this.buttonStates = {
      forward: false,      // 前进按钮状态
      backward: false,     // 后退按钮状态
      left: false,         // 左转按钮状态
      right: false,        // 右转按钮状态
      brake: false,        // 刹车按钮状态
      parking: false,      // 驻车按钮状态
      gear1: false,        // 1档按钮状态
      gear2: false,        // 2档按钮状态
      gear3: false,        // 3档按钮状态
      swing: false,        // 摇摆按钮状态
      remote: false,       // 中控按钮状态
      panelLowSpeed: false,  // 车面板低速状态
      panelHighSpeed: false, // 车面板高速状态
      customSpeed: false   // 自定义速度状态
    }

    // 🎯 指令映射表
    this.commandMap = {
      forward: { cmd: 0x10, data: 0x01, name: '前进' },
      backward: { cmd: 0x10, data: 0x02, name: '后退' },
      stop: { cmd: 0x10, data: 0x00, name: '停止' },
      brake: { cmd: 0x10, data: 0x03, name: '刹车' },
      left: { cmd: 0x14, data: 0x01, name: '左转' },
      right: { cmd: 0x14, data: 0x02, name: '右转' },
      stopTurn: { cmd: 0x14, data: 0x00, name: '停转' },
      parking: { cmd: 0x1A, data: 0x01, name: '驻车' },
      swing: { cmd: 0x20, data: 0x01, name: '摇摆' },
      remote: { cmd: 0x1E, data: 0x00, name: '中控' },
      panelLowSpeed: { cmd: 0x16, data: 0x14, name: '车面板低速' },  // 默认20%
      panelHighSpeed: { cmd: 0x18, data: 0x64, name: '车面板高速' }, // 默认100%
      gear1: { cmd: 0x12, data: 0x1E, name: '1档' },
      gear2: { cmd: 0x12, data: 0x32, name: '2档' },
      gear3: { cmd: 0x12, data: 0x64, name: '3档' }
    }

    // 系统信息
    this.system = 'unknown'

    // 回调函数
    this.onConnectionChange = null
    this.onDataReceived = null
    this.onError = null
    this.onSendData = null  // 🎯 发送数据记录回调

    // 🎯 初始化状态管理
    this.isInitialized = false
    this.isInitializing = false
    this.bluetoothStateListenerSet = false  // 蓝牙状态监听器是否已设置
  }

  /**
   * 初始化系统信息
   * 检测当前运行平台（iOS/Android），影响BLE数据封装方式
   */
  initSystemInfo() {
    try {
      const { system } = wx.getSystemInfoSync()
      this.system = system
      console.log('🔧 检测到系统:', system)
    } catch (error) {
      console.error('❌ 获取系统信息失败:', error)
      this.system = 'Android'
    }
  }

  /**
   * 🎯 刷新蓝牙配置
   * 重新从本地存储获取最新的配置信息
   */
  refreshConfig() {
    try {
      // 重新获取配置
      this.config = getCurrentConfig()

      // 更新设备信息
      this.targetDeviceName = this.config.deviceName
      this.targetAddress = this.config.address

      console.log('🔄 蓝牙配置已刷新:', {
        deviceName: this.targetDeviceName,
        address: this.targetAddress
      })

      return true
    } catch (error) {
      console.error('❌ 刷新蓝牙配置失败:', error)
      return false
    }
  }

  /**
   * 🎯 新架构：设置按钮状态
   * @param {string} buttonKey - 按钮键名
   * @param {boolean} isPressed - 是否按下
   */
  setButtonState(buttonKey, isPressed) {
    console.log(`🎯 设置按钮状态: ${buttonKey} = ${isPressed}`)
    this.buttonStates[buttonKey] = isPressed

    // 立即处理状态变化
    this.processButtonStates()
  }

  /**
   * 🎯 获取当前按钮状态
   * @returns {Object} 按钮状态对象
   */
  getButtonStates() {
    return { ...this.buttonStates }
  }

  /**
   * 🎯 强制同步按钮状态（用于修复状态不一致问题）
   * @param {Object} states - 外部状态对象
   */
  syncButtonStates(states) {
    console.log('🔄 同步按钮状态:', states)

    // 同步左右转状态
    if (states.isLeftTurnActive !== this.buttonStates.left) {
      console.log(`🔄 同步左转状态: ${this.buttonStates.left} → ${states.isLeftTurnActive}`)
      this.buttonStates.left = states.isLeftTurnActive
    }

    if (states.isRightTurnActive !== this.buttonStates.right) {
      console.log(`🔄 同步右转状态: ${this.buttonStates.right} → ${states.isRightTurnActive}`)
      this.buttonStates.right = states.isRightTurnActive
    }
  }

  /**
   * 🎯 紧急重置转向状态（用于修复状态混乱）
   */
  async emergencyResetTurnStates() {
    console.log('🚨 紧急重置转向状态')

    // 强制停止所有转向广播
    const turnCommands = ['left', 'right']

    for (const commandKey of turnCommands) {
      try {
        const serverInfo = this.broadcastServers.get(commandKey)
        if (serverInfo && serverInfo.server) {
          console.log(`🛑 紧急停止 ${commandKey} 广播`)

          // 直接调用停止，不等待回调
          serverInfo.server.stopAdvertising({
            success: () => console.log(`✅ 紧急停止 ${commandKey} 成功`),
            fail: (error) => console.log(`⚠️ 紧急停止 ${commandKey} 失败:`, error)
          })

          // 立即更新状态
          serverInfo.isActive = false
        }
      } catch (error) {
        console.error(`❌ 紧急停止 ${commandKey} 异常:`, error)
      }
    }

    // 重置按钮状态
    this.buttonStates.left = false
    this.buttonStates.right = false

    console.log('🧹 转向状态紧急重置完成')
  }

  /**
   * 🎯 强制停止所有转向广播（紧急修复方法）
   */
  async forceStopAllTurnBroadcasts() {
    console.log('🚨 强制停止所有转向广播')

    const turnCommands = ['left', 'right']

    for (const commandKey of turnCommands) {
      try {
        const serverInfo = this.broadcastServers.get(commandKey)
        if (serverInfo && serverInfo.server) {
          console.log(`🛑 强制停止 ${commandKey} 广播`)

          // 直接调用停止广播，不等待回调
          serverInfo.server.stopAdvertising({
            success: () => {
              console.log(`✅ 强制停止 ${commandKey} 成功`)
            },
            fail: (error) => {
              console.log(`⚠️ 强制停止 ${commandKey} 失败:`, error)
            }
          })

          // 立即更新状态
          serverInfo.isActive = false
          this.buttonStates[commandKey] = false
        }
      } catch (error) {
        console.error(`❌ 强制停止 ${commandKey} 异常:`, error)
      }
    }

    console.log('🧹 所有转向广播已强制停止')
  }

  /**
   * 🎯 新架构：根据按钮状态处理指令发送 (简化版本)
   * 只处理激活的按钮，停止操作由具体的stop方法处理
   */
  async processButtonStates() {
    console.log('🔄 处理按钮状态:', this.buttonStates)
    // 🎯 简化：不在这里自动处理，由具体的按钮方法直接调用广播
    // 避免重复调用 sendIndependentCommand
  }

  /**
   * 🎯 新架构：为每个指令创建独立的BLE服务器
   * @param {string} commandKey - 指令键名
   * @returns {Promise} 服务器创建结果
   */
  async createIndependentServer(commandKey) {
    try {
      const res = await wx.createBLEPeripheralServer()
      console.log(`✅ 为指令 ${commandKey} 创建独立服务器成功:`, res.server.serverId)

      // 存储到独立服务器映射中
      this.broadcastServers.set(commandKey, {
        server: res.server,
        serverId: res.server.serverId,
        isActive: false,
        lastCommand: null
      })

      return res.server
    } catch (error) {
      console.error(`❌ 为指令 ${commandKey} 创建服务器失败:`, error)
      throw error
    }
  }

  /**
   * 🎯 新架构：获取或创建指令的独立服务器
   * @param {string} commandKey - 指令键名
   * @returns {Promise} 服务器实例
   */
  async getOrCreateServer(commandKey) {
    // 检查是否已有该指令的服务器
    if (this.broadcastServers.has(commandKey)) {
      const serverInfo = this.broadcastServers.get(commandKey)

      // 如果已有实际服务器，直接复用
      if (serverInfo.server) {
        console.log(`� 复用预创建的指令 ${commandKey} 服务器`)
        return serverInfo.server
      }
    }

    // 如果没有预创建的服务器，创建新的独立服务器
    console.log(`🆕 为指令 ${commandKey} 创建新服务器`)
    return await this.createIndependentServer(commandKey)
  }

  /**
   * 🎯 新架构：启动独立广播 (简化版本)
   * @param {string} commandKey - 指令键名
   */
  async startIndependentBroadcast(commandKey) {
    try {
      // 检查是否已在广播中
      const serverInfo = this.broadcastServers.get(commandKey)
      if (serverInfo && serverInfo.isActive) {
        console.log(`🔄 指令 ${commandKey} 已在广播中，跳过重复启动`)
        return { success: true }
      }

      // 🎯 对于移动指令，先停止其他移动指令的广播，避免冲突
      if (['forward', 'backward'].includes(commandKey)) {
        const conflictCommands = ['forward', 'backward'].filter(cmd => cmd !== commandKey)
        for (const conflictCmd of conflictCommands) {
          const conflictServer = this.broadcastServers.get(conflictCmd)
          if (conflictServer && conflictServer.isActive) {
            console.log(`🛑 停止冲突指令 ${conflictCmd} 的广播`)
            await this.stopIndependentCommand(conflictCmd)
          }
        }
      }

      // 获取或创建独立服务器
      const server = await this.getOrCreateServer(commandKey)

      // 获取指令
      let hexCommand = this.getPresetCommand(commandKey)
      if (!hexCommand) {
        const commandConfig = this.commandMap[commandKey]
        if (!commandConfig) {
          console.error(`❌ 未找到指令配置: ${commandKey}`)
          return { success: false, error: '未找到指令配置' }
        }

        // 生成指令
        const command = this.generateCarCommand(commandConfig.cmd, commandConfig.data)
        hexCommand = command
          .map((b) => b.toString(16).padStart(2, "0"))
          .join("")
          .toUpperCase()
      }

      console.log(`📤 发送独立指令 ${this.getCommandName(commandKey)}:`, hexCommand)

      // 开始独立广播
      await this.startIndependentAdvertising(commandKey, server, hexCommand)

      return { success: true }
    } catch (error) {
      console.error(`❌ 启动独立广播 ${commandKey} 失败:`, error)
      return { success: false, error }
    }
  }



  /**
   * 🎯 新架构：停止独立指令
   * @param {string} commandKey - 指令键名
   */
  async stopIndependentCommand(commandKey) {
    console.log(`🔍 尝试停止独立指令 ${commandKey} 的广播`)

    const serverInfo = this.broadcastServers.get(commandKey)
    if (!serverInfo) {
      console.log(`⚠️ 指令 ${commandKey} 的服务器不存在`)
      return
    }

    console.log(`🔍 指令 ${commandKey} 服务器状态: isActive=${serverInfo.isActive}`)

    // 强制停止广播，不管状态如何
    try {
      await new Promise((resolve) => {
        serverInfo.server.stopAdvertising({
          success: (res) => {
            console.log(`🛑 停止独立指令 ${commandKey} 的广播成功`)
            resolve(res)
          },
          fail: (error) => {
            console.log(`⚠️ 停止独立指令 ${commandKey} 广播失败（可能已停止）:`, error)
            resolve() // 即使失败也继续，可能广播已经停止了
          }
        })
      })

      // 强制更新状态
      serverInfo.isActive = false
      serverInfo.lastCommand = null

    } catch (error) {
      console.error(`❌ 停止独立指令 ${commandKey} 失败:`, error)
      // 即使出错也要更新状态
      serverInfo.isActive = false
    }
  }

  /**
   * 🎯 新架构：开始独立广播
   * @param {string} commandKey - 指令键名
   * @param {Object} server - 服务器实例
   * @param {string} hexCommand - 十六进制指令
   */
  async startIndependentAdvertising(commandKey, server, hexCommand) {
    try {
      // 检测系统平台并生成BLE数据包
      const systemStr = (this.system || '').toLowerCase()
      const isIos = systemStr.indexOf('ios') >= 0
      const actPayload = generateData(hexCommand, isIos)

      // 根据平台生成Service UUIDs
      const uuids = getServiceUUIDs(actPayload, isIos && systemStr.indexOf('13.') >= 0)

      console.log(`🍎 ${commandKey} 独立广播配置:`, {
        isIos: isIos,
        deviceName: isIos ? '11' : '',
        serviceUuids: isIos ? uuids : [],
        manufacturerData: isIos ? [] : [{
          manufacturerId: '0x00C7',
          manufacturerSpecificData: actPayload,
        }]
      })

      // 开始独立广播 - 使用服务器的startAdvertising方法
      await new Promise((resolve, reject) => {
        server.startAdvertising({
          advertiseTimeout: 0,
          advertiseRequest: {
            connectable: true,
            deviceName: isIos ? '11' : '',
            serviceUuids: isIos ? uuids : [],
            manufacturerData: isIos ? [] : [{
              manufacturerId: '0x00C7',
              manufacturerSpecificData: actPayload,
            }]
          },
          powerLevel: 'high',
          success: (res) => {
            console.log(`✅ 独立指令 ${commandKey} 广播启动成功`)
            resolve(res)
          },
          fail: (error) => {
            console.log(`❌ 独立指令 ${commandKey} 广播失败:`, error)
            reject(error)
          }
        })
      })

      // 更新服务器状态
      const serverInfo = this.broadcastServers.get(commandKey)
      if (serverInfo) {
        serverInfo.isActive = true
        serverInfo.lastCommand = hexCommand
      }

      console.log(`✅ 独立指令 ${commandKey} 广播启动成功`)

      // 记录发送数据
      if (this.onSendData) {
        this.onSendData(hexCommand, `独立指令-${commandKey}`, {
          broadcastStatus: 'success',
          broadcastMessage: `独立指令 ${commandKey} 发送成功`
        })
      }

    } catch (error) {
      console.error(`❌ 独立指令 ${commandKey} 广播失败:`, error)

      // 记录发送失败
      if (this.onSendData) {
        this.onSendData(hexCommand, `独立指令-${commandKey}`, {
          broadcastStatus: 'failed',
          broadcastMessage: `独立指令 ${commandKey} 发送失败: ${error.message}`
        })
      }
    }
  }

  /**
   * 🎯 创建广播服务器（不启动广播）
   * @param {string} commandKey - 指令键名
   */
  async preCreateBroadcastServer(commandKey) {
    if (this.broadcastServers.has(commandKey)) {
      console.log(`🔄 指令 ${commandKey} 的服务器已存在`)
      return
    }

    try {
      const serverId = Math.floor(Math.random() * 2000000000)
      const server = wx.createBLEPeripheralServer()

      // 🎯 添加服务到服务器
      const serviceUuid = '0000FFF0-0000-1000-8000-00805F9B34FB'
      await new Promise((resolve, reject) => {
        server.addService({
          uuid: serviceUuid,
          primary: true,
          success: resolve,
          fail: reject
        })
      })

      console.log(`✅ 为指令 ${commandKey} 添加服务成功`)

      this.broadcastServers.set(commandKey, {
        server: server,
        serverId: serverId,
        isActive: false,
        hexCommand: this.getCommandHex(commandKey),
        serviceUuid: serviceUuid
      })

      console.log(`✅ 创建指令 ${commandKey} 的服务器成功: ${serverId}`)
    } catch (error) {
      console.error(`❌ 创建指令 ${commandKey} 的服务器失败:`, error)
      throw error
    }
  }



  /**
   * 🎯 获取指令对应的十六进制命令
   * @param {string} commandKey - 指令键名
   */
  getCommandHex(commandKey) {
    const commandMap = {
      'forward': '81000210011300FA',
      'backward': '81000210021400FA',
      'left': '81000214011700FA',
      'right': '81000214021800FA',
      'brake': '81000210031500FA',
      'parking': '8100021A011D00FA',
      'swing': '81000220012300FA',      // 摇摆开启
      'remote': '8100021E002000FA',     // 中控只遥控
      'panelLowSpeed': '81000216142C00FA',   // 车面板低速20%
      'panelHighSpeed': '810002186480FA',    // 车面板高速100%
      'gear1': '810002121E3200FA',
      'gear2': '810002123C5000FA',
      'gear3': '810002125A6E00FA',
      'statusQuery': '81000224002600FA'  // 🎯 状态查询指令
    }
    return commandMap[commandKey] || '81000210001200FA'
  }

  /**
   * 🎯 获取指令对应的中文名称
   * @param {string} commandKey - 指令键名
   */
  getCommandName(commandKey) {
    const nameMap = {
      'forward': '前进',
      'backward': '后退',
      'left': '左转',
      'right': '右转',
      'brake': '刹车',
      'parking': '驻车',
      'gear1': '1档',
      'gear2': '2档',
      'gear3': '3档',
      'swing': '摇摆',      // 摇摆开启
      'remote': '中控',
      'panelLowSpeed': '车面板低速',
      'panelHighSpeed': '车面板高速',
      'statusQuery': '状态查询',  // 🎯 状态查询
    }
    return nameMap[commandKey] || commandKey
  }
  /**
   * 🎯 获取预设指令（兼容旧版本）
   * @param {string} commandKey - 指令键名
   */
  getPresetCommand(commandKey) {
    return this.getCommandHex(commandKey)
  }



  /**
   * 🎯 新架构：停止所有独立广播服务器
   */
  stopAllIndependentBroadcasts() {
    console.log('🛑 停止所有独立广播服务器')

    this.broadcastServers.forEach((serverInfo, commandKey) => {
      try {
        if (serverInfo.server && serverInfo.isActive) {
          serverInfo.server.stopAdvertising({
            success: () => {
              console.log(`✅ 停止指令 ${commandKey} 的广播`)
            },
            fail: (error) => {
              console.error(`❌ 停止指令 ${commandKey} 广播失败:`, error)
            }
          })
          serverInfo.isActive = false
        }
        // 关闭服务器
        if (serverInfo.server) {
          serverInfo.server.close()
          console.log(`🔒 关闭指令 ${commandKey} 的服务器`)
        }
      } catch (error) {
        console.error(`❌ 停止指令 ${commandKey} 广播失败:`, error)
      }
    })

    // 清空服务器映射
    this.broadcastServers.clear()

    // 重置所有按钮状态
    Object.keys(this.buttonStates).forEach(key => {
      this.buttonStates[key] = false
    })

    // 🎯 重置蓝牙初始化状态，确保下次进入页面能重新初始化
    this.isInitialized = false
    this.isInitializing = false
    this.advertiseReady = false
    this.discoveryReady = false
    this.discoveryStarted = false

    console.log('🧹 所有独立广播已清理完成，蓝牙状态已重置')
  }

  /**
   * 页面卸载时的清理函数
   * 停止所有蓝牙活动，释放资源
   */
  onNoneLoad() {
    console.log('🧹 开始清理蓝牙资源')

    try {
      // 1. 停止所有独立广播服务器
      this.stopAllIndependentBroadcasts()

      // 2. 停止设备扫描
      this.stopBluetoothDevicesDiscovery()

      // 3. 关闭所有BLE服务器
      this.closeAllBLEServers()

      // 4. 关闭蓝牙适配器
      this.closeBluetoothAdapters()

      // 5. 重置所有状态
      this.resetAllStates()

      console.log('✅ 蓝牙资源清理完成')
    } catch (error) {
      console.error('❌ 蓝牙资源清理失败:', error)
    }
  }

  /**
   * 关闭所有BLE服务器
   */
  closeAllBLEServers() {
    console.log('🔒 关闭所有BLE服务器')

    this.broadcastServers.forEach((serverInfo, commandKey) => {
      try {
        if (serverInfo.server) {
          console.log(`🔒 关闭指令 ${commandKey} 的服务器`)
          serverInfo.server.close()
        }
      } catch (error) {
        console.error(`❌ 关闭服务器 ${commandKey} 失败:`, error)
      }
    })

    // 清空服务器映射
    this.broadcastServers.clear()
  }

  /**
   * 关闭蓝牙适配器
   */
  closeBluetoothAdapters() {
    console.log('🔒 关闭蓝牙适配器')

    try {
      wx.closeBluetoothAdapter({
        success: () => {
          console.log('✅ 蓝牙适配器关闭成功')
        },
        fail: (error) => {
          console.log('⚠️ 蓝牙适配器关闭失败:', error)
        }
      })
    } catch (error) {
      console.error('❌ 关闭蓝牙适配器异常:', error)
    }
  }

  /**
   * 重置所有状态
   */
  resetAllStates() {
    console.log('🔄 重置所有状态')

    // 重置初始化状态
    this.isInitialized = false
    this.isInitializing = false
    this.advertiseReady = false
    this.discoveryReady = false
    this.discoveryStarted = false

    // 重置按钮状态
    Object.keys(this.buttonStates).forEach(key => {
      this.buttonStates[key] = false
    })

    // 清理响应检测
    this.waitingForResponse = false

    console.log('✅ 状态重置完成')
  }

  /**
   * 生成车控制指令（按照V1.10协议）
   * @param {number} commandByte - 命令字 (0x10/0x12/0x14)
   * @param {number} dataByte - 数据字节
   * @param {number} deviceAddress - 设备地址 (默认使用配置中的地址)
   * @returns {Array} 指令字节数组
   */
  generateCarCommand(commandByte, dataByte, deviceAddress = null) {
    // 如果没有指定设备地址，使用配置中的默认地址
    if (deviceAddress === null) {
      deviceAddress = this.deviceAddress
    }
    // 协议格式：81 [设备地址] 02 [命令字] [数据] [校验和低位] [校验和高位] FA
    const byte1 = deviceAddress // 设备地址码
    const byte2 = 0x02 // 数据长度
    const byte3 = commandByte // 命令字 (0x10/0x12/0x14)
    const byte4 = dataByte // 数据

    // 校验计算：BYTE1 到 BYTE4 的累加
    const checksum = (byte1 + byte2 + byte3 + byte4) & 0xffff
    const sumL = checksum & 0xff // 低8位
    const sumH = (checksum >> 8) & 0xff // 高8位

    return [
      0x81, // 控制起始符
      byte1, // 设备地址码
      byte2, // 数据长度
      byte3, // 命令字
      byte4, // 数据
      sumL, // 校验和低位
      sumH, // 校验和高位
      0xfa, // 控制结束符
    ]
  }

  // ===== 新架构的按钮控制方法 =====

  /**
   * 发送前进指令 (独立广播版本)
   */
  async sendForward() {
    console.log('🔍 sendForward 被调用')

    if (!this.advertiseReady) {
      console.log('⚠️ 广播适配器未就绪，跳过前进指令')
      return
    }

    // 🎯 启动前进服务器的广播
    this.setButtonState('forward', true)
    await this.startIndependentBroadcast('forward')
  }

  /**
   * 停止前进指令 (独立广播版本)
   */
  stopForward() {
    console.log('🔍 stopForward 被调用')
    this.setButtonState('forward', false)
    // 🎯 真正停止对应的广播
    this.stopIndependentCommand('forward')
  }

  /**
   * 发送后退指令 (独立广播版本)
   */
  async sendBackward() {
    console.log('🔍 sendBackward 被调用')

    if (!this.advertiseReady) {
      console.log('⚠️ 广播适配器未就绪，跳过后退指令')
      return
    }

    // 🎯 启动后退服务器的广播
    this.setButtonState('backward', true)
    await this.startIndependentBroadcast('backward')
  }

  /**
   * 停止后退指令
   */
  stopBackward() {
    console.log('🔍 stopBackward 被调用')
    this.setButtonState('backward', false)
    // 🎯 真正停止对应的广播
    this.stopIndependentCommand('backward')
  }

  /**
   * 发送左转指令 (新架构适配)
   */
  async sendLeft() {
    console.log('🔍 sendLeft 被调用')

    // 🎯 修复：先停止右转，避免冲突
    if (this.buttonStates.right) {
      console.log('🔄 左转：先停止右转广播')
      await this.stopIndependentCommand('right')
      this.setButtonState('right', false)
    }

    this.setButtonState('left', true)
    await this.startIndependentBroadcast('left')
  }

  /**
   * 停止左转指令
   */
  stopLeft() {
    console.log('🔍 stopLeft 被调用')
    this.setButtonState('left', false)
    // 🎯 真正停止对应的广播
    this.stopIndependentCommand('left')
  }

  /**
   * 发送右转指令 (新架构适配)
   */
  async sendRight() {
    console.log('🔍 sendRight 被调用')

    // 🎯 修复：先停止左转，避免冲突
    if (this.buttonStates.left) {
      console.log('🔄 右转：先停止左转广播')
      await this.stopIndependentCommand('left')
      this.setButtonState('left', false)
    }

    this.setButtonState('right', true)
    await this.startIndependentBroadcast('right')
  }

  /**
   * 停止右转指令
   */
  stopRight() {
    console.log('🔍 stopRight 被调用')
    this.setButtonState('right', false)
    // 🎯 真正停止对应的广播
    this.stopIndependentCommand('right')
  }

  /**
   * 发送刹车指令 (新架构适配)
   */
  async sendBrake() {
    console.log('🔍 sendBrake 被调用')

    // 🎯 刹车时立即停止所有移动指令
    const movementCommands = ['forward', 'backward']
    for (const cmd of movementCommands) {
      const serverInfo = this.broadcastServers.get(cmd)
      if (serverInfo && serverInfo.isActive) {
        console.log(`🛑 刹车：立即停止 ${cmd} 指令`)
        await this.stopIndependentCommand(cmd)
      }
    }

    this.setButtonState('brake', true)
    await this.startIndependentBroadcast('brake')
  }

  /**
   * 停止刹车指令
   */
  stopBrake() {
    console.log('🔍 stopBrake 被调用')
    this.setButtonState('brake', false)
    // 🎯 真正停止对应的广播
    this.stopIndependentCommand('brake')
  }

  /**
   * 发送驻车指令 (新架构适配) - 切换状态
   */
  async sendParking() {
    // 获取当前驻车状态，切换到相反状态
    const currentState = this.buttonStates.parking || false
    const newState = !currentState

    console.log('🔍 sendParking 被调用, 当前状态:', currentState, '新状态:', newState)
    console.log('🔍 advertiseReady 状态:', this.advertiseReady)

    // 更新按钮状态
    this.setButtonState('parking', newState)

    // 更新指令映射中的驻车数据
    const parkingData = newState ? 0x01 : 0x00  // 1=开启驻车，0=关闭驻车

    // 临时更新commandMap中的parking指令
    const originalParkingData = this.commandMap.parking?.data
    if (this.commandMap.parking) {
      this.commandMap.parking.data = parkingData
    }

    try {
      // 生成对应的驻车指令
      const command = this.generateCarCommand(0x1A, parkingData)
      const hexCommand = command
        .map((b) => b.toString(16).padStart(2, "0"))
        .join("")
        .toUpperCase()

      console.log(`📤 发送驻车指令 (${newState ? '开启' : '关闭'}):`, hexCommand)

      // 获取或创建独立服务器
      const server = await this.getOrCreateServer('parking')

      // 直接启动广播
      await this.startIndependentAdvertising('parking', server, hexCommand)

      // 延迟停止广播，让指令有时间发送
      setTimeout(() => {
        this.stopIndependentCommand('parking')
        // 恢复原始数据
        if (this.commandMap.parking && originalParkingData !== undefined) {
          this.commandMap.parking.data = originalParkingData
        }
      }, 500)
    } catch (error) {
      console.error('❌ 驻车指令发送失败:', error)
    }
  }

  /**
   * 发送档位指令 (新架构适配)
   */
  async sendGear(gear) {
    console.log('🔍 sendGear 被调用，档位:', gear)

    // 🎯 首先检查是否已初始化，未初始化时只设置状态不发送指令
    if (!this.isInitialized) {
      console.log('⚠️ BLE未初始化，只设置档位状态，不发送指令')
      // 先清除所有档位状态
      this.setButtonState('gear1', false)
      this.setButtonState('gear2', false)
      this.setButtonState('gear3', false)
      // 设置对应档位状态
      if (gear >= 1 && gear <= 3) {
        this.setButtonState(`gear${gear}`, true)
      }
      return
    }

    // 先清除所有档位状态
    this.setButtonState('gear1', false)
    this.setButtonState('gear2', false)
    this.setButtonState('gear3', false)

    // 设置对应档位状态并发送一次性指令
    if (gear >= 1 && gear <= 3) {
      this.setButtonState(`gear${gear}`, true)

      // 🎯 检查广播适配器和服务器是否就绪
      if (!this.advertiseReady) {
        console.log('⚠️ 广播适配器未就绪，跳过档位指令发送')
        return
      }

      // 🎯 检查对应档位的服务器是否存在
      const gearKey = `gear${gear}`
      const serverInfo = this.broadcastServers.get(gearKey)
      if (!serverInfo || !serverInfo.server) {
        console.log(`⚠️ 档位 ${gear} 服务器未就绪，等待服务器创建完成`)
        // 等待一下再重试
        setTimeout(() => {
          this.sendGear(gear)
        }, 500)
        return
      }

      try {
        // 发送档位指令（一次性，不持续广播）
        await this.startIndependentBroadcast(gearKey)

        // 延迟停止档位广播，让指令有时间发送和设备响应
        setTimeout(() => {
          this.stopIndependentCommand(gearKey)
          // 保持按钮状态，不重置为false
        }, 1000)  // 增加到1秒，给设备更多时间响应
      } catch (error) {
        console.error(`❌ 档位 ${gear} 指令发送失败:`, error)
      }
    }
  }

  /**
   * 发送自定义速度指令 (新架构适配)
   */
  sendCustomSpeed(speedValue) {
    console.log('🔍 sendCustomSpeed 被调用，速度值:', speedValue)

    // 动态更新指令映射
    this.commandMap.customSpeed = { cmd: 0x12, data: speedValue, name: '自定义速度' }
    this.setButtonState('customSpeed', true)
  }

  /**
   * 停止自定义速度指令
   */
  stopCustomSpeed() {
    console.log('🔍 stopCustomSpeed 被调用')
    this.setButtonState('customSpeed', false)
  }

  /**
   * 🎯 发送状态查询指令 (新架构适配)
   * 使用命令字 0x24，数据字节 0x00 查询设备状态
   */
  async sendStatusQuery() {
    console.log('🔍 sendStatusQuery 被调用，查询设备状态')

    // 🎯 首先检查是否已初始化
    if (!this.isInitialized) {
      console.log('⚠️ BLE未初始化，无法发送状态查询指令')
      return false
    }

    // 🎯 检查广播适配器是否就绪
    if (!this.advertiseReady) {
      console.log('⚠️ 广播适配器未就绪，跳过状态查询指令发送')
      return false
    }

    try {
      // 生成状态查询指令：命令字 0x24，数据字节 0x00
      const command = this.generateCarCommand(0x24, 0x00)
      const hexCommand = command
        .map((b) => b.toString(16).padStart(2, "0"))
        .join("")
        .toUpperCase()

      console.log(`🔍 发送状态查询指令:`, hexCommand)

      // 创建临时的状态查询服务器（如果不存在）
      const statusQueryKey = 'statusQuery'
      if (!this.broadcastServers.has(statusQueryKey)) {
        await this.createIndependentServer(statusQueryKey)
      }

      // 临时更新指令映射
      this.commandMap.statusQuery = { cmd: 0x24, data: 0x00, name: '状态查询' }

      // 发送状态查询指令（一次性，不持续广播）
      await this.startIndependentBroadcast(statusQueryKey)

      // 延迟停止状态查询广播，让指令有时间发送
      setTimeout(() => {
        this.stopIndependentCommand(statusQueryKey)
      }, 500)  // 0.5秒足够发送指令

      return true
    } catch (error) {
      console.error(`❌ 状态查询指令发送失败:`, error)
      return false
    }
  }

  // ===== 初始化和管理方法 =====



  /**
   * 检查蓝牙是否开启
   */
  checkBluetoothEnabled() {
    return new Promise((resolve) => {
      wx.getBluetoothAdapterState({
        success: (res) => {
          console.log('🔍 蓝牙状态检查:', res)
          resolve(res.available)
        },
        fail: (error) => {
          console.log('⚠️ 蓝牙状态检查失败:', error)
          resolve(false)
        }
      })
    })
  }

  /**
   * 初始化蓝牙功能 - 新架构版本
   * 按顺序初始化各个组件
   */
  async initBluetooth() {
    if (this.isInitialized || this.isInitializing) {
      console.log('🔄 蓝牙已初始化或正在初始化，跳过')
      return
    }

    console.log('🔧 开始蓝牙工具初始化 - 新架构')
    this.isInitializing = true

    try {
      // 🎯 1. 先检查蓝牙是否开启
      const bluetoothEnabled = await this.checkBluetoothEnabled()
      if (!bluetoothEnabled) {
        throw new Error('蓝牙未开启')
      }

      // 2. 获取系统信息和初始化配置
      this.initSystemInfo()
      this.initPayload()

      // 🎯 刷新蓝牙配置（确保使用最新的设备信息）
      this.refreshConfig()

      // 3. 异步初始化适配器（广播适配器会自动预创建服务器）
      await Promise.all([
        this.initAdvertiseAdapter(),  // 初始化广播适配器（发送功能）+ 预创建服务器
        this.initDiscoveryAdapter()   // 初始化发现适配器（接收功能）
      ])

      this.isInitialized = true
      console.log('✅ 蓝牙工具初始化完成 - 新架构')

      // 🎯 初始化成功，不通知连接状态（由页面管理）
      console.log(`🔗 蓝牙初始化成功`)

    } catch (error) {
      console.error('❌ 蓝牙工具初始化失败:', error)
      this.isInitialized = false

      // 🎯 不通知连接状态（由页面管理）

      // 🎯 静默记录错误，不弹Toast（等用户操作时再提示）
      console.log('🔇 蓝牙初始化失败，等待用户操作时提示:', error.message)

      throw error
    } finally {
      this.isInitializing = false
    }
  }

  /**
   * 初始化载荷数据
   * 从本地存储读取配置数据
   */
  initPayload() {
    try {
      saveConfig(DEFAULT_CONFIG)
      console.log('✅ 配置初始化完成')
    } catch (error) {
      console.error('❌ 配置初始化失败:', error)
    }
  }

  /**
   * 初始化广播适配器（发送功能）
   * 以外设模式打开蓝牙适配器，用于发送BLE广播
   */
  initAdvertiseAdapter() {
    return new Promise((resolve, reject) => {
      // iOS需要先检查权限状态，避免频繁请求
      if (this.system && this.system.toLowerCase().indexOf('ios') >= 0) {
        // 先检查授权状态
        wx.getSetting({
          success: (res) => {
            if (res.authSetting['scope.bluetooth'] === true) {
              console.log('✅ iOS蓝牙权限已授权，直接初始化')
              this.openAdvertiseAdapter().then(resolve).catch(reject)
            } else if (res.authSetting['scope.bluetooth'] === false) {
              console.log('❌ iOS蓝牙权限被拒绝，尝试继续初始化')
              this.openAdvertiseAdapter().then(resolve).catch(reject)
            } else {
              console.log('🔍 iOS蓝牙权限未设置，请求授权')
              wx.authorize({
                scope: 'scope.bluetooth',
                success: () => {
                  console.log('✅ iOS蓝牙权限获取成功')
                  this.openAdvertiseAdapter().then(resolve).catch(reject)
                },
                fail: (error) => {
                  console.log('⚠️ iOS蓝牙权限获取失败，继续尝试', error)
                  this.openAdvertiseAdapter().then(resolve).catch(reject)
                }
              })
            }
          },
          fail: (error) => {
            console.log('⚠️ 获取设置失败，直接尝试初始化', error)
            this.openAdvertiseAdapter().then(resolve).catch(reject)
          }
        })
      } else {
        this.openAdvertiseAdapter().then(resolve).catch(reject)
      }
    })
  }

  /**
   * 打开广播适配器
   */
  openAdvertiseAdapter() {
    return new Promise((resolve, reject) => {
      wx.openBluetoothAdapter({
        mode: 'peripheral',  // 外设模式，用于广播数据
        success: async (res) => {
          console.log('initAdvertiseAdapter success', res)
          this.advertiseReady = true
          await this.createBLEPeripheralServer()  // 🎯 成功后立即创建BLE外设服务器
          console.log('✅ 广播适配器初始化成功 - 新架构')
          resolve(res)
        },
        fail: (res) => {
          console.log("initAdvertiseAdapter ble unavailable!", res)
          reject(res)
        }
      })
    })
  }

  /**
   * 创建BLE外设服务器
   * 🎯 广播适配器就绪后，预创建所有独立BLE服务器
   */
  async createBLEPeripheralServer() {
    console.log('🎯 广播适配器就绪，开始预创建所有独立BLE服务器')

    // 标记广播功能就绪
    this.advertiseReady = true

    // 🎯 预创建所有按钮对应的独立BLE服务器
    await this.preCreateAllServers()

    console.log('✅ 所有独立BLE服务器预创建完成')
  }

  /**
   * 🎯 预创建所有独立BLE服务器
   */
  async preCreateAllServers() {
    const commands = ['forward', 'backward', 'left', 'right', 'brake', 'parking', 'gear1', 'gear2', 'gear3', 'swing', 'remote', 'panelLowSpeed', 'panelHighSpeed']

    console.log('🏗️ 开始批量预创建独立BLE服务器')

    for (const commandKey of commands) {
      try {
        await this.createIndependentServer(commandKey)
        console.log(`✅ 预创建 ${commandKey} 服务器成功`)
      } catch (error) {
        console.error(`❌ 预创建 ${commandKey} 服务器失败:`, error)
      }
    }

    console.log('🎉 所有独立BLE服务器预创建完成')
  }

  /**
   * 🎯 启动指定服务器的广播
   * @param {string} commandKey - 指令键名
   */
  async startServerBroadcast(commandKey) {
    try {
      const serverInfo = this.broadcastServers.get(commandKey)
      if (!serverInfo) {
        console.error(`❌ ${commandKey} 服务器不存在`)
        return
      }

      if (serverInfo.isActive) {
        console.log(`⚠️ ${commandKey} 广播已在进行中`)
        return
      }

      // 使用预设的指令开始广播
      const hexCommand = serverInfo.hexCommand
      console.log(`📤 启动 ${commandKey} 广播: ${hexCommand}`)

      // 生成BLE数据包
      const systemStr = (this.system || '').toLowerCase()
      const isIos = systemStr.indexOf('ios') >= 0
      const actPayload = generateData(hexCommand, isIos)
      const uuids = getServiceUUIDs(actPayload, isIos && systemStr.indexOf('13.') >= 0)

      // 开始广播
      await new Promise((resolve, reject) => {
        serverInfo.server.startAdvertising({
          advertiseTimeout: 0,
          advertiseRequest: {
            connectable: true,
            deviceName: isIos ? '11' : '',
            serviceUuids: isIos ? uuids : [],
            manufacturerData: isIos ? [] : [{
              manufacturerId: '0x00C7',
              manufacturerSpecificData: actPayload,
            }]
          },
          powerLevel: 'high',
          success: (res) => {
            console.log(`✅ ${commandKey} 广播启动成功`)
            serverInfo.isActive = true
            resolve(res)
          },
          fail: (error) => {
            console.error(`❌ ${commandKey} 广播启动失败:`, error)
            reject(error)
          }
        })
      })

    } catch (error) {
      console.error(`❌ 启动 ${commandKey} 广播失败:`, error)
    }
  }



  /**
   * 🎯 创建单个独立BLE服务器
   */
  async createIndependentServer(commandKey) {
    try {
      // 如果已存在，跳过
      if (this.broadcastServers.has(commandKey)) {
        console.log(`⚠️ ${commandKey} 服务器已存在，跳过创建`)
        return
      }

      // 创建新的独立服务器
      const res = await wx.createBLEPeripheralServer()
      const server = res.server
      const serverId = res.server.serverId

      // 保存服务器信息
      this.broadcastServers.set(commandKey, {
        server: server,
        serverId: serverId,
        isActive: false,
        hexCommand: this.getCommandHex(commandKey)
      })

      console.log(`✅ 创建 ${commandKey} 独立服务器成功: ${serverId}`)

    } catch (error) {
      console.error(`❌ 创建 ${commandKey} 独立服务器失败:`, error)
      throw error
    }
  }

  /**
   * 🎯 简化方法：创建独立服务器并发送指令
   */
  async createAndSendCommand(commandKey, hexCommand) {
    try {
      // 如果已有服务器在广播，先停止
      if (this.broadcastServers.has(commandKey)) {
        const existingServer = this.broadcastServers.get(commandKey)
        if (existingServer.isActive) {
          console.log(`🛑 停止已有的 ${commandKey} 广播`)
          existingServer.server.stopAdvertising()

          existingServer.isActive = false
        }
      }

      // 创建新的独立服务器
      const res = await wx.createBLEPeripheralServer()
      const server = res.server
      const serverId = res.server.serverId

      // 保存服务器信息
      this.broadcastServers.set(commandKey, {
        server: server,
        serverId: serverId,
        isActive: false
      })

      console.log(`✅ 创建 ${commandKey} 独立服务器成功: ${serverId}`)

      // 发送指令
      await this.startServerAdvertising(commandKey, hexCommand)

    } catch (error) {
      console.error(`❌ 创建 ${commandKey} 服务器失败:`, error)
    }
  }

  /**
   * 🎯 启动服务器广播
   */
  async startServerAdvertising(commandKey, hexCommand) {
    const serverInfo = this.broadcastServers.get(commandKey)
    if (!serverInfo) {
      console.error(`❌ ${commandKey} 服务器不存在`)
      return
    }

    try {
      const actPayload = this.buildPayload(hexCommand)
      const uuids = this.buildServiceUuids(actPayload)
      const isIos = this.system && this.system.toLowerCase().indexOf('ios') >= 0

      console.log(`📤 发送独立指令 ${this.getCommandName(commandKey)}: ${hexCommand}`)

      const advertiseConfig = {
        advertiseTimeout: 0,
        advertiseRequest: {
          connectable: true,
          deviceName: isIos ? '11' : '',
          serviceUuids: isIos ? uuids : [],
          manufacturerData: isIos ? [] : [{
            manufacturerId: '0x00C7',
            manufacturerSpecificData: actPayload,
          }]
        },
        powerLevel: 'high'
      }

      console.log(`🍎 ${commandKey} 独立广播配置:`, {
        isIos: isIos,
        deviceName: advertiseConfig.advertiseRequest.deviceName,
        serviceUuids: advertiseConfig.advertiseRequest.serviceUuids,
        manufacturerData: advertiseConfig.advertiseRequest.manufacturerData
      })

      await new Promise((resolve, reject) => {
        serverInfo.server.startAdvertising({
          ...advertiseConfig,
          success: (res) => {
            console.log(`✅ 独立指令 ${commandKey} 广播启动成功`)
            serverInfo.isActive = true
            resolve(res)
          },
          fail: (error) => {
            console.error(`❌ 独立指令 ${commandKey} 广播失败:`, error)
            reject(error)
          }
        })
      })

    } catch (error) {
      console.error(`❌ ${commandKey} 广播启动失败:`, error)
    }
  }

  /**
   * 🎯 简化方法：停止并销毁指令服务器
   */
  stopAndDestroyCommand(commandKey) {
    try {
      const serverInfo = this.broadcastServers.get(commandKey)
      if (!serverInfo) {
        console.log(`⚠️ ${commandKey} 服务器不存在，无需停止`)
        return
      }

      if (serverInfo.isActive) {
        console.log(`🛑 停止 ${commandKey} 广播`)
        serverInfo.server.stopAdvertising()
        serverInfo.isActive = false
      }

      // 销毁服务器
      console.log(`🗑️ 销毁 ${commandKey} 服务器`)
      this.broadcastServers.delete(commandKey)

    } catch (error) {
      console.error(`❌ 停止 ${commandKey} 失败:`, error)
    }
  }

  /**
   * 创建所有独立的BLE服务器
   */
  async createAllIndependentServers() {
    const commands = [
      'forward', 'backward', 'left', 'right', 'brake', 'parking',
      'gear1', 'gear2', 'gear3', 'swing', 'remote', 'panelLowSpeed', 'panelHighSpeed'
    ]

    console.log('� 开始批量创建独立BLE服务器')

    for (const command of commands) {
      try {
        await this.createSingleIndependentServer(command)
        console.log(`✅ 创建 ${command} 独立服务器成功`)
      } catch (error) {
        console.error(`❌ 创建 ${command} 独立服务器失败:`, error)
      }
    }

    console.log('🎉 所有独立BLE服务器创建完成')
  }

  /**
   * 创建单个独立的BLE服务器
   */
  async createSingleIndependentServer(commandKey) {
    // 🎯 wx.createBLEPeripheralServer() 返回Promise，需要await
    const res = await wx.createBLEPeripheralServer()
    const server = res.server
    const serverId = res.server.serverId

    // 保存服务器信息
    this.broadcastServers.set(commandKey, {
      server: server,
      serverId: serverId,
      isActive: false,
      hexCommand: this.getCommandHex(commandKey)
    })

    return server
  }

  /**
   * 初始化设备发现适配器（接收功能）
   * 🎯 使用默认模式（中心设备模式），与旧版本保持一致
   */
  initDiscoveryAdapter(retryCount = 0) {
    return new Promise((resolve, reject) => {
      wx.openBluetoothAdapter({
        // 🎯 不指定mode，使用默认的中心设备模式（与旧版本一致）
        success: (res) => {
          console.log('initDiscoveryAdapter success', res)
          this.discoveryReady = true
          this.initBluetoothDevicesDiscovery()  // 初始化蓝牙成功后 初始化设备发现
          console.log('✅ 发现适配器初始化成功 - 新架构')
          resolve(res)
        },
        fail: (res) => {
          console.log("initDiscoveryAdapter ble unavailable!", res)

          // 🎯 检查是否是调试环境的蓝牙限制
          if (res.errMsg && res.errMsg.includes('目前蓝牙调试功能暂不支持')) {
            console.log('⚠️ 当前环境不支持蓝牙调试功能，跳过发现适配器初始化')
            this.discoveryReady = false
            resolve(res) // 即使失败也resolve，因为这不是致命错误
            return
          }

          // 🎯 iOS特殊处理：如果失败，尝试重新初始化（与旧版本一致）
          if (this.system && this.system.toLowerCase().indexOf('ios') >= 0) {
            // 🎯 限制重试次数，避免无限重试
            if (retryCount < 5) {
              console.log(`🍎 iOS蓝牙发现适配器初始化失败，尝试重新初始化... (${retryCount + 1}/5)`)
              // 🎯 立即重试，与旧版本保持一致
              this.initDiscoveryAdapter(retryCount + 1).then(resolve).catch(reject)
            } else {
              console.log('🍎 iOS蓝牙发现适配器重试次数已达上限，跳过接收功能')
              this.discoveryReady = false
              resolve(res) // 即使失败也resolve，因为这不是致命错误
            }
          } else {
            reject(res)
          }
        }
      })
    })
  }

  /**
   * 初始化蓝牙设备发现功能
   * 配置设备扫描参数，但不立即开始扫描
   */
  initBluetoothDevicesDiscovery() {
    // 防止重复初始化
    if (this.discoveryStarted) {
      console.log('🔄 设备发现已启动，跳过重复初始化')
      return
    }

    console.log('🔍 开始初始化蓝牙设备发现功能')

    wx.startBluetoothDevicesDiscovery({
      allowDuplicatesKey: true,  // 允许重复上报同一设备，用于实时数据接收
      powerLevel: "high",        // 高功率扫描，增加接收范围
      success: (res) => {
        console.log('✅ startBluetoothDevicesDiscovery success! ', res)
        this.discoveryStarted = true
        this.onBluetoothDeviceFound() // 设置设备发现监听器
      },
      fail: (res) => {
        console.log('❌ startBluetoothDevicesDiscovery failed! ', res)

        // 🎯 如果是适配器未打开的错误，标记发现功能不可用但不影响发送功能
        if (res.errCode === 10000) {
          console.log('⚠️ 蓝牙适配器状态异常，跳过设备发现功能，但保持发送功能正常')
          this.discoveryReady = false
        }
      }
    })
  }

  /**
   * 开始蓝牙设备发现
   * 设置设备发现监听器并标记扫描状态
   */
  startBluetoothDevicesDiscovery() {
    // 防止重复启动
    if (this.discoveryStarted)
      return

    // 设置设备发现监听器，用来接收其他设备发送的数据
    this.onBluetoothDeviceFound()

    // 标记扫描已开始
    this.discoveryStarted = true
  }

  /**
   * 停止蓝牙设备发现R
   * 取消监听器并更新状态
   */
  stopBluetoothDevicesDiscovery() {
    // 检查是否正在扫描
    if (!this.discoveryStarted)
      return

    this.discoveryStarted = false  // 标记扫描已停止
    wx.offBluetoothDeviceFound()   // 取消设备发现监听器
  }

  /**
   * 设备发现事件监听器
   * 当扫描到蓝牙设备时触发，处理接收到的数据
   */
  onBluetoothDeviceFound() {
    const filterName = this.filterName  // 获取设备名称过滤器

    wx.onBluetoothDeviceFound((res) => {
      res.devices.forEach((device) => {
        // 🎯 调试：显示所有发现的设备
        // console.log(`🔍 发现设备: ${device.localName || '无名称'}, ID: ${device.deviceId}`)

        // 1. 设备名称过滤：只处理匹配filterName的设备
        if (!device.localName || device.localName != filterName) {
          // console.log(`⚠️ 设备名称不匹配，期望: ${filterName}, 实际: ${device.localName}`)
          return
        }

        console.log(`✅ 匹配到目标设备: ${device.localName}`)

        // 2. 更新设备列表（避免重复添加）
        const foundDevices = this.devices
        const idx = inArray(foundDevices, 'deviceId', device.deviceId)

        if (idx === -1) {
          // 新设备：在设备列表中没找到，添加到列表末尾
          this.devices.push(device)
        } else {
          // 已存在设备，更新信息
          this.devices[idx] = device
        }

        // 🎯 关键：每次找到设备都更新状态（即使是重复发现）
        this.deviceFound = true
        this.lastDeviceFoundTime = Date.now()

        // 🎯 调试：只在状态变化或每5秒打印一次
        if (!this._lastLogTime || Date.now() - this._lastLogTime > 5000) {
          console.log(`🎯 设备持续在线: deviceFound=${this.deviceFound}, lastFoundTime=${this.lastDeviceFoundTime}`)
          this._lastLogTime = Date.now()
        }

        // 3. 提取并处理广播数据
        let hexData = ab2hex(device.advertisData)  // 转换为十六进制字符串
        hexData = hexData.substring(4)             // 去掉前4位（BLE头部）
        device.data = hexData

        // 检查是否是新数据（避免重复打印相同数据）
        const currentDataKey = `${device.deviceId}_${hexData}`
        const isNewData = !this.lastPrintedData || this.lastPrintedData !== currentDataKey

        if (isNewData) {
          this.lastPrintedData = currentDataKey
          console.log('=== 📦 新设备数据 ===')
          console.log('📱 设备:', device.localName)
          console.log('🆔 ID:', device.deviceId.substring(0, 8) + '...')
          console.log('🔋 信号:', device.RSSI, 'dBm')
          console.log('📊 数据:', hexData)
          console.log('⏰ 时间:', new Date().toLocaleTimeString())
          console.log('===================')
        }

        // 4. 生成时间戳并格式化接收数据
        const myDate = new Date();
        const time = myDate.toLocaleTimeString() + " " + myDate.getMilliseconds()
        const receiveData = { time, data: hexData }

        // 5. 将新数据添加到列表开头（最新的在上面）
        console.log(`📡 准备发送接收数据到页面:`, receiveData)
        if (this.onDataReceived) {
          this.onDataReceived(receiveData)
          console.log(`✅ 接收数据已发送到页面`)
        } else {
          console.log(`⚠️ 数据接收回调未设置`)
        }
      })
    })
  }

  // ===== 回调函数设置方法 =====

  /**
   * 设置连接状态变化回调
   * @param {Function} callback - 回调函数 (isConnected) => {}
   */
  setConnectionCallback(callback) {
    this.onConnectionChange = callback
  }

  /**
   * 设置数据接收回调
   * @param {Function} callback - 回调函数 (data) => {}
   */
  setDataCallback(callback) {
    this.onDataReceived = callback
  }

  /**
   * 设置错误回调
   * @param {Function} callback - 回调函数 (error) => {}
   */
  setErrorCallback(callback) {
    this.onError = callback
  }

  /**
   * 设置发送数据记录回调
   * @param {Function} callback - 回调函数 (hexCommand, commandType) => {}
   */
  setSendDataCallback(callback) {
    this.onSendData = callback
  }

  /**
   * 设置蓝牙状态变化回调
   * @param {Function} callback - 回调函数 (isEnabled) => {}
   */
  setBluetoothStateCallback(callback) {
    this.onBluetoothStateChange = callback
  }

  /**
   * 清空接收数据列表
   * 用户点击"清空"按钮时调用
   */
  clickClearResult() {
    // 通过回调通知页面清空数据
    if (this.onDataReceived) {
      this.onDataReceived('CLEAR_DATA')
    }
  }

  /**
   * 获取当前按钮状态
   * @returns {Object} 当前所有按钮的状态
   */
  getButtonStates() {
    return { ...this.buttonStates }
  }

  /**
   * 获取当前活跃的广播服务器信息
   * @returns {Array} 活跃的服务器信息列表
   */
  getActiveBroadcasts() {
    const activeBroadcasts = []
    this.broadcastServers.forEach((serverInfo, commandKey) => {
      if (serverInfo.isActive) {
        activeBroadcasts.push({
          commandKey,
          serverId: serverInfo.serverId,
          lastCommand: serverInfo.lastCommand
        })
      }
    })
    return activeBroadcasts
  }

  /**
   * 重置初始化状态
   * 用于重连时清理状态
   */
  resetInitializationState() {
    console.log('🔄 重置蓝牙初始化状态 - 新架构')
    this.isInitialized = false
    this.isInitializing = false
    this.advertiseReady = false
    this.discoveryReady = false
    this.discoveryStarted = false

    // 停止所有独立广播
    this.stopAllIndependentBroadcasts()
  }

  /**
   * 🎯 设置蓝牙适配器状态监听
   */
  setupBluetoothStateListener() {
    // 🎯 防止重复设置监听器
    if (this.bluetoothStateListenerSet) {
      console.log('🔄 蓝牙状态监听器已设置，跳过')
      return
    }

    console.log('🔍 设置蓝牙适配器状态监听')

    wx.onBluetoothAdapterStateChange((res) => {
      console.log('📡 蓝牙适配器状态变化:', res)

      if (res.available) {
        console.log('✅ 蓝牙已开启')
        // 🎯 蓝牙重新开启，通知页面可以重新初始化
        console.log('🔄 蓝牙重新开启，通知页面重新初始化')
        // 通知页面蓝牙状态变化，让页面决定是否重新初始化
        if (this.onBluetoothStateChange) {
          this.onBluetoothStateChange(true)
        }
      } else {
        console.log('❌ 蓝牙已关闭')
        // 蓝牙关闭，更新状态
        this.handleBluetoothDisconnected()
      }
    })

    // 标记监听器已设置
    this.bluetoothStateListenerSet = true
    console.log('✅ 蓝牙状态监听器设置完成')
  }

  /**
   * 🎯 处理蓝牙断开连接
   */
  handleBluetoothDisconnected() {
    console.log('🔌 处理蓝牙断开连接')

    // 停止所有广播
    this.stopAllIndependentBroadcasts()

    // 重置状态
    this.isInitialized = false
    this.advertiseReady = false
    this.discoveryReady = false
    this.discoveryStarted = false

    // 🎯 不通知连接状态（由页面管理）

    // 通知页面显示断开提示
    if (this.onError) {
      this.onError('蓝牙连接已断开，请检查蓝牙设置')
    }
  }

  /**
   * 发送摇摆指令 (新架构适配)
   * @param {number} swingValue - 摇摆值 (0: 关闭摇摆, 1: 打开摇摆)
   */
  async sendSwing(swingValue) {
    console.log('🔍 sendSwing 被调用，摇摆值:', swingValue)

    try {
      // 🎯 构建摇摆指令：81 00 02 20 01 23 00 FA (打开) 或 81 00 02 20 00 22 00 FA (关闭)
      const command = this.generateCarCommand(0x20, swingValue)
      const hexCommand = command
        .map((b) => b.toString(16).padStart(2, "0"))
        .join("")
        .toUpperCase()

      console.log(`📤 发送摇摆指令 (${swingValue ? '打开' : '关闭'}):`, hexCommand)

      // 获取或创建独立服务器
      const server = await this.getOrCreateServer('swing')

      // 直接启动广播
      await this.startIndependentAdvertising('swing', server, hexCommand)

      // 延迟停止广播，让指令有时间发送
      setTimeout(() => {
        this.stopIndependentCommand('swing')
      }, 500)

      console.log(`✅ 摇摆指令发送成功: ${swingValue ? '打开' : '关闭'}`)

    } catch (error) {
      console.error('❌ 发送摇摆指令失败:', error)
    }
  }

  /**
   * 发送中控指令 (新架构适配)
   * @param {number} remoteValue - 中控值 (0: 只接受遥控, 1: 遥控和面板)
   */
  async sendRemoteControl(remoteValue) {
    console.log('🔍 sendRemoteControl 被调用，中控值:', remoteValue)

    try {
      // 🎯 构建中控指令：81 00 02 1E 00 20 00 FA (只遥控) 或 81 00 02 1E 01 21 00 FA (遥控和面板)
      const command = this.generateCarCommand(0x1E, remoteValue)
      const hexCommand = command
        .map((b) => b.toString(16).padStart(2, "0"))
        .join("")
        .toUpperCase()

      console.log(`📤 发送中控指令 (${remoteValue ? '遥控和面板' : '只接受遥控'}):`, hexCommand)

      // 获取或创建独立服务器
      const server = await this.getOrCreateServer('remote')

      // 直接启动广播
      await this.startIndependentAdvertising('remote', server, hexCommand)

      // 延迟停止广播，让指令有时间发送
      setTimeout(() => {
        this.stopIndependentCommand('remote')
      }, 500)

      console.log(`✅ 中控指令发送成功: ${remoteValue ? '遥控和面板' : '只接受遥控'}`)

    } catch (error) {
      console.error('❌ 发送中控指令失败:', error)
    }
  }

  /**
   * 发送车内控制面板低速值设置指令
   * @param {number} speedValue - 速度值 (0-100) 表示为百分比
   */
  async sendPanelLowSpeed(speedValue) {
    console.log('🔍 sendPanelLowSpeed 被调用，低速值:', speedValue)

    try {
      // 🎯 构建车面板低速指令：81 00 02 16 XX XX XX FA
      const command = this.generateCarCommand(0x16, speedValue)
      const hexCommand = command
        .map((b) => b.toString(16).padStart(2, "0"))
        .join("")
        .toUpperCase()

      console.log(`📤 发送车面板低速指令 (${speedValue}%):`, hexCommand)

      // 获取或创建独立服务器
      const server = await this.getOrCreateServer('panelLowSpeed')

      // 直接启动广播
      await this.startIndependentAdvertising('panelLowSpeed', server, hexCommand)

      // 延迟停止广播，让指令有时间发送
      setTimeout(() => {
        this.stopIndependentCommand('panelLowSpeed')
      }, 500)

      console.log(`✅ 车面板低速指令发送成功: ${speedValue}%`)

    } catch (error) {
      console.error('❌ 发送车面板低速指令失败:', error)
    }
  }

  /**
   * 发送车内控制面板高速值设置指令
   * @param {number} speedValue - 速度值 (0-100) 表示为百分比
   */
  async sendPanelHighSpeed(speedValue) {
    console.log('🔍 sendPanelHighSpeed 被调用，高速值:', speedValue)

    try {
      // 🎯 构建车面板高速指令：81 00 02 18 XX XX XX FA
      const command = this.generateCarCommand(0x18, speedValue)
      const hexCommand = command
        .map((b) => b.toString(16).padStart(2, "0"))
        .join("")
        .toUpperCase()

      console.log(`📤 发送车面板高速指令 (${speedValue}%):`, hexCommand)

      // 获取或创建独立服务器
      const server = await this.getOrCreateServer('panelHighSpeed')

      // 直接启动广播
      await this.startIndependentAdvertising('panelHighSpeed', server, hexCommand)

      // 延迟停止广播，让指令有时间发送
      setTimeout(() => {
        this.stopIndependentCommand('panelHighSpeed')
      }, 500)

      console.log(`✅ 车面板高速指令发送成功: ${speedValue}%`)

    } catch (error) {
      console.error('❌ 发送车面板高速指令失败:', error)
    }
  }

  /**
   * 🎯 获取设备搜索状态
   * @returns {Object} 设备搜索状态信息
   */
  getDeviceSearchStatus() {
    try {
      const now = Date.now()
      const timeSinceLastFound = now - this.lastDeviceFoundTime
      const isDeviceOnline = this.deviceFound && timeSinceLastFound < this.deviceSearchTimeout

      const status = {
        isSearching: this.discoveryStarted,  // 是否正在搜索
        deviceFound: this.deviceFound,       // 是否找到设备
        isDeviceOnline: isDeviceOnline,      // 设备是否在线
        lastFoundTime: this.lastDeviceFoundTime,  // 最后发现时间
        timeSinceLastFound: timeSinceLastFound,   // 距离最后发现的时间
        targetDeviceName: this.targetDeviceName   // 目标设备名称
      }

      return status
    } catch (error) {
      console.error('❌ getDeviceSearchStatus 方法异常:', error)
      return {
        isSearching: false,
        deviceFound: false,
        isDeviceOnline: false,
        lastFoundTime: 0,
        timeSinceLastFound: 0,
        targetDeviceName: 'TEMP'
      }
    }
  }

  /**
   * 🎯 重置设备搜索状态（当确认设备不在线时调用）
   */
  resetDeviceSearchStatus() {
    console.log('🔄 重置设备搜索状态')
    this.deviceFound = false
    this.lastDeviceFoundTime = 0
  }

  /**
   * 🎯 强制重置设备搜索状态（用于页面重新进入时）
   */
  forceResetDeviceSearchStatus() {
    console.log('🔄 强制重置设备搜索状态（页面重新进入）')
    this.deviceFound = false
    this.lastDeviceFoundTime = 0
    this.discoveryStarted = false // 也重置搜索启动状态
  }
}

export default new ToyCarBleUnifiedNew()
